{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/button", "title": "<PERSON><PERSON>", "category": "design", "parent": ["core/buttons"], "description": "Prompt visitors to take action with a button-style link.", "keywords": ["link"], "textdomain": "default", "attributes": {"tagName": {"type": "string", "enum": ["a", "button"], "default": "a"}, "type": {"type": "string", "default": "button"}, "textAlign": {"type": "string"}, "url": {"type": "string", "source": "attribute", "selector": "a", "attribute": "href", "role": "content"}, "title": {"type": "string", "source": "attribute", "selector": "a,button", "attribute": "title", "role": "content"}, "text": {"type": "rich-text", "source": "rich-text", "selector": "a,button", "role": "content"}, "linkTarget": {"type": "string", "source": "attribute", "selector": "a", "attribute": "target", "role": "content"}, "rel": {"type": "string", "source": "attribute", "selector": "a", "attribute": "rel", "role": "content"}, "placeholder": {"type": "string"}, "backgroundColor": {"type": "string"}, "textColor": {"type": "string"}, "gradient": {"type": "string"}, "width": {"type": "number"}}, "supports": {"anchor": true, "splitting": true, "align": false, "alignWide": false, "color": {"__experimentalSkipSerialization": true, "gradients": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "typography": {"__experimentalSkipSerialization": ["fontSize", "lineHeight", "fontFamily", "fontWeight", "fontStyle", "textTransform", "textDecoration", "letterSpacing"], "fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalWritingMode": true, "__experimentalDefaultControls": {"fontSize": true}}, "reusable": false, "shadow": {"__experimentalSkipSerialization": true}, "spacing": {"__experimentalSkipSerialization": true, "padding": ["horizontal", "vertical"], "__experimentalDefaultControls": {"padding": true}}, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true, "__experimentalSkipSerialization": true, "__experimentalDefaultControls": {"color": true, "radius": true, "style": true, "width": true}}, "interactivity": {"clientNavigation": true}}, "styles": [{"name": "fill", "label": "Fill", "isDefault": true}, {"name": "outline", "label": "Outline"}], "editorStyle": "wp-block-button-editor", "style": "wp-block-button", "selectors": {"root": ".wp-block-button .wp-block-button__link", "typography": {"writingMode": ".wp-block-button"}}}