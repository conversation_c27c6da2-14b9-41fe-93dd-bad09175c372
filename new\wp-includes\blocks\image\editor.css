.wp-block-image.wp-block-image .block-editor-media-placeholder.is-small{
  min-height:60px;
}

figure.wp-block-image:not(.wp-block){
  margin:0;
}

.wp-block-image{
  position:relative;
}
.wp-block-image .is-applying img,.wp-block-image.is-transient img{
  opacity:.3;
}
.wp-block-image figcaption img{
  display:inline;
}
.wp-block-image .components-spinner{
  left:50%;
  margin:0;
  position:absolute;
  top:50%;
  transform:translate(-50%, -50%);
}

.wp-block-image__placeholder{
  aspect-ratio:4/3;
}
.wp-block-image__placeholder.has-illustration:before{
  background:#fff;
  opacity:.8;
}
.wp-block-image__placeholder .components-placeholder__illustration{
  opacity:.1;
}

.wp-block-image .components-resizable-box__container{
  display:table;
}
.wp-block-image .components-resizable-box__container img{
  display:block;
  height:inherit;
  width:inherit;
}

.block-editor-block-list__block[data-type="core/image"] .block-editor-block-toolbar .block-editor-url-input__button-modal{
  left:0;
  margin:-1px 0;
  position:absolute;
  right:0;
}
@media (min-width:600px){
  .block-editor-block-list__block[data-type="core/image"] .block-editor-block-toolbar .block-editor-url-input__button-modal{
    margin:-1px;
  }
}

[data-align=full]>.wp-block-image img,[data-align=wide]>.wp-block-image img{
  height:auto;
  width:100%;
}

.wp-block[data-align=center]>.wp-block-image,.wp-block[data-align=left]>.wp-block-image,.wp-block[data-align=right]>.wp-block-image{
  display:table;
}
.wp-block[data-align=center]>.wp-block-image>figcaption,.wp-block[data-align=left]>.wp-block-image>figcaption,.wp-block[data-align=right]>.wp-block-image>figcaption{
  caption-side:bottom;
  display:table-caption;
}

.wp-block[data-align=left]>.wp-block-image{
  margin:.5em 1em .5em 0;
}

.wp-block[data-align=right]>.wp-block-image{
  margin:.5em 0 .5em 1em;
}

.wp-block[data-align=center]>.wp-block-image{
  margin-left:auto;
  margin-right:auto;
  text-align:center;
}

.wp-block[data-align]:has(>.wp-block-image){
  position:relative;
}

.wp-block-image__crop-area{
  max-width:100%;
  overflow:hidden;
  position:relative;
  width:100%;
}
.wp-block-image__crop-area .reactEasyCrop_Container{
  pointer-events:auto;
}
.wp-block-image__crop-area .reactEasyCrop_Container .reactEasyCrop_Image{
  border:none;
  border-radius:0;
}

.wp-block-image__crop-icon{
  align-items:center;
  display:flex;
  justify-content:center;
  min-width:48px;
  padding:0 8px;
}
.wp-block-image__crop-icon svg{
  fill:currentColor;
}

.wp-block-image__zoom .components-popover__content{
  min-width:260px;
  overflow:visible !important;
}

.wp-block-image__toolbar_content_textarea__container{
  padding:8px;
}

.wp-block-image__toolbar_content_textarea{
  width:250px;
}