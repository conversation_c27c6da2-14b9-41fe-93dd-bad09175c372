# XAMPP WordPress Setup Guide

## Overview
This guide will help you set up your WordPress installations to work with XAMPP locally.

## Prerequisites
- XAMPP installed and running
- Apache and MySQL services started in XAMPP Control Panel

## Database Setup

### 1. Create Databases in phpMyAdmin
1. Open your browser and go to `http://localhost/phpmyadmin`
2. Click on "Databases" tab
3. Create two new databases:
   - `wordpress_main` (for the root WordPress installation)
   - `wordpress_new` (for the WordPress installation in the "new" folder)

### 2. Import Your Data (if you have existing data)
If you have database backups from your previous hosting:
1. Select the database you want to import to
2. Click "Import" tab
3. Choose your SQL file and click "Go"

## File Structure Setup

### 1. Move Files to XAMPP htdocs
Your current folder structure should be placed in XAMPP's htdocs directory:
```
C:\xampp\htdocs\.well-known\
├── wp-config.php (main WordPress)
├── index.php
├── wp-admin/
├── wp-content/
├── wp-includes/
├── new/
│   ├── wp-config.php (secondary WordPress)
│   ├── index.php
│   ├── wp-admin/
│   ├── wp-content/
│   └── wp-includes/
└── ...
```

## Configuration Changes Made

### Database Settings Updated:
- **Main WordPress**: Now uses database `wordpress_main`
- **New WordPress**: Now uses database `wordpress_new`
- **Username**: Changed to `root` (XAMPP default)
- **Password**: Changed to empty string (XAMPP default)
- **Host**: Changed to `localhost` (removed port specification)

### Development Settings Enabled:
- `WP_DEBUG` set to `true`
- `WP_DEBUG_LOG` enabled
- `WP_DEBUG_DISPLAY` enabled
- `SCRIPT_DEBUG` enabled

### URL Rewriting Updated:
- .htaccess files updated to work with the `.well-known` folder structure

## Access Your WordPress Sites

After completing the setup:

### Main WordPress Installation:
- URL: `http://localhost/.well-known/`
- Admin: `http://localhost/.well-known/wp-admin/`

### Secondary WordPress Installation:
- URL: `http://localhost/.well-known/new/`
- Admin: `http://localhost/.well-known/new/wp-admin/`

## Troubleshooting

### Common Issues:

1. **Database Connection Error**
   - Ensure MySQL is running in XAMPP
   - Verify database names exist in phpMyAdmin
   - Check wp-config.php settings

2. **404 Errors**
   - Ensure mod_rewrite is enabled in Apache
   - Check .htaccess files are present
   - Verify file permissions

3. **White Screen of Death**
   - Check error logs in `wp-content/debug.log`
   - Verify all WordPress files are present
   - Check PHP error logs in XAMPP

### File Permissions
If you encounter permission issues:
- Ensure XAMPP has read/write access to the files
- On Windows, this is usually not an issue

## Next Steps

1. Start XAMPP (Apache and MySQL)
2. Create the databases in phpMyAdmin
3. Access your WordPress sites via the URLs above
4. If this is a fresh installation, run through WordPress setup
5. If importing data, restore your database backups

## Development Tips

- Use the debug.log file for troubleshooting: `wp-content/debug.log`
- Keep XAMPP error logs open while developing
- Consider using a local development plugin for easier URL management
- Make regular backups of your local databases during development
