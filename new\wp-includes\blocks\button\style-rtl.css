.wp-block-button__link{
  align-content:center;
  box-sizing:border-box;
  cursor:pointer;
  display:inline-block;
  height:100%;
  text-align:center;
  word-break:break-word;
}
.wp-block-button__link.aligncenter{
  text-align:center;
}
.wp-block-button__link.alignright{
  text-align:right;
}

:where(.wp-block-button__link){
  border-radius:9999px;
  box-shadow:none;
  padding:calc(.667em + 2px) calc(1.333em + 2px);
  text-decoration:none;
}

.wp-block-button[style*=text-decoration] .wp-block-button__link{
  text-decoration:inherit;
}

.wp-block-buttons>.wp-block-button.has-custom-width{
  max-width:none;
}
.wp-block-buttons>.wp-block-button.has-custom-width .wp-block-button__link{
  width:100%;
}
.wp-block-buttons>.wp-block-button.has-custom-font-size .wp-block-button__link{
  font-size:inherit;
}
.wp-block-buttons>.wp-block-button.wp-block-button__width-25{
  width:calc(25% - var(--wp--style--block-gap, .5em)*.75);
}
.wp-block-buttons>.wp-block-button.wp-block-button__width-50{
  width:calc(50% - var(--wp--style--block-gap, .5em)*.5);
}
.wp-block-buttons>.wp-block-button.wp-block-button__width-75{
  width:calc(75% - var(--wp--style--block-gap, .5em)*.25);
}
.wp-block-buttons>.wp-block-button.wp-block-button__width-100{
  flex-basis:100%;
  width:100%;
}

.wp-block-buttons.is-vertical>.wp-block-button.wp-block-button__width-25{
  width:25%;
}
.wp-block-buttons.is-vertical>.wp-block-button.wp-block-button__width-50{
  width:50%;
}
.wp-block-buttons.is-vertical>.wp-block-button.wp-block-button__width-75{
  width:75%;
}

.wp-block-button.is-style-squared,.wp-block-button__link.wp-block-button.is-style-squared{
  border-radius:0;
}

.wp-block-button.no-border-radius,.wp-block-button__link.no-border-radius{
  border-radius:0 !important;
}

:root :where(.wp-block-button .wp-block-button__link.is-style-outline),:root :where(.wp-block-button.is-style-outline>.wp-block-button__link){
  border:2px solid;
  padding:.667em 1.333em;
}
:root :where(.wp-block-button .wp-block-button__link.is-style-outline:not(.has-text-color)),:root :where(.wp-block-button.is-style-outline>.wp-block-button__link:not(.has-text-color)){
  color:currentColor;
}
:root :where(.wp-block-button .wp-block-button__link.is-style-outline:not(.has-background)),:root :where(.wp-block-button.is-style-outline>.wp-block-button__link:not(.has-background)){
  background-color:initial;
  background-image:none;
}